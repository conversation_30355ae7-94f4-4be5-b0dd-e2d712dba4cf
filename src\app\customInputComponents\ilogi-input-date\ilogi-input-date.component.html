<div class="form-group">
  <!-- Read-only display -->
  <div *ngIf="readonly" class="readonly-container">
    <label *ngIf="fieldLabel" [class.bold-label]="readonly">
      {{fieldLabel}} <span *ngIf="mandatory" class="required-indicator">*</span>
    </label>
    <div class="show-data">
      {{ value | date:'dd/MM/yyyy' }}
    </div>
  </div>

  <!-- Material UI Form Field for date input -->
  <mat-form-field *ngIf="!readonly" appearance="outline" class="w-100">
    <mat-label *ngIf="fieldLabel">
      {{fieldLabel}} <span *ngIf="mandatory" class="required-indicator">*</span>
    </mat-label>

    <input
      matInput
      [matDatepicker]="picker"
      [value]="value"
      (dateChange)="onDateChange($event.value)"
      [placeholder]="placeholder"
      [id]="fieldId"
      (click)="picker.open()"
      (mouseenter)="showErrorOnFieldHover()"
      (mouseleave)="hideErrorOnFieldHoverOut()"
      [attr.aria-invalid]="(submitted && errors) || dateRangeError ? 'true' : 'false'"
      [attr.aria-describedby]="errorFieldId"
      [disabled]="isDisabled"
      [min]="minDate"
      [max]="maxDate"
      readonly
    />

    <mat-datepicker-toggle matIconSuffix [for]="picker">
      <mat-icon matDatepickerToggleIcon>calendar_today</mat-icon>
    </mat-datepicker-toggle>

    <mat-datepicker [startAt]="maxDate" #picker></mat-datepicker>

    <!-- Error messages -->
    <mat-error *ngIf="submitted && errors" [id]="errorFieldId">
      <ng-container *ngFor="let item of (errors || {} | keyvalue); let i = index">
        <div *ngIf="i === 0" class="error-message">
          {{ errorMessages[item.key] || item.value?.message }}
        </div>
      </ng-container>
      <ng-container *ngIf="errors as errors">
        <div *ngIf="errors['custom']?.status" class="error-message">
          {{ errors['custom'].message }}
        </div>
      </ng-container>
    </mat-error>

    <mat-error *ngIf="dateRangeError">
      <div class="error-message">{{ dateRangeError }}</div>
    </mat-error>
  </mat-form-field>

  <!-- Hover error display for better UX -->
  <div
    *ngIf="((submitted && errors) || dateRangeError) && isHovered && !readonly"
    class="hover-error-tooltip"
    [id]="errorFieldId + '_hover'"
  >
    <ng-container *ngFor="let item of (errors || {} | keyvalue); let i = index">
      <div *ngIf="i === 0" class="tooltip-error-text">
        {{ errorMessages[item.key] || item.value?.message }}
      </div>
    </ng-container>
    <ng-container *ngIf="errors as errors">
      <div *ngIf="errors['custom']?.status" class="tooltip-error-text">
        {{ errors['custom'].message }}
      </div>
    </ng-container>
    <div *ngIf="dateRangeError" class="tooltip-error-text">
      {{ dateRangeError }}
    </div>
  </div>
</div>
