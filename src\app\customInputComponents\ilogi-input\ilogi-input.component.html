<div class="form-group">
  <!-- Read-only display -->
  <div *ngIf="readonly" class="readonly-container">
    <label *ngIf="!hideLabel" [class.bold-label]="readonly">
      {{ fieldLabel }} <span *ngIf="mandatory" class="required-indicator">*</span>
    </label>
    <div class="show-data" *ngIf="pipe === 'currency'">
      {{ fieldExactVal ?? (checkIsNan(value) ? value : (value | currency : "INR")) }}
    </div>
    <div class="show-data" *ngIf="pipe !== 'currency'">
      {{ fieldExactVal ?? value }}
    </div>
  </div>

  <!-- Material UI Form Field for inputs -->
  <mat-form-field *ngIf="!readonly" appearance="outline" class="w-100">
    <mat-label *ngIf="!hideLabel">
      {{ fieldLabel }} <span *ngIf="mandatory" class="required-indicator">*</span>
    </mat-label>

    <!-- Text Input -->
    <input
      matInput
      *ngIf="type !== 'textarea'"
      appBlockCopyPaste
      [blockCopyPaste]="appBlockCopyPaste"
      [type]="type"
      [placeholder]="placeholder"
      [id]="fieldId"
      [value]="value"
      (input)="onInputChange($event)"
      [attr.maxlength]="maxlength"
      [disabled]="isDisabled"
      [attr.aria-invalid]="submitted && errors ? 'true' : 'false'"
      [attr.aria-describedby]="errorFieldId"
      (mouseenter)="showErrorOnFieldHover()"
      (mouseleave)="hideErrorOnFieldHoverOut()"
      (blur)="changeBlur($event)"
    />

    <!-- Textarea -->
    <textarea
      matInput
      *ngIf="type === 'textarea'"
      [value]="value"
      (input)="onInputChange($event)"
      [attr.maxlength]="maxlength"
      [placeholder]="placeholder"
      [id]="fieldId"
      [rows]="rows"
      [disabled]="isDisabled"
      [attr.aria-invalid]="submitted && errors ? 'true' : 'false'"
      [attr.aria-describedby]="errorFieldId"
      (mouseenter)="showErrorOnFieldHover()"
      (mouseleave)="hideErrorOnFieldHoverOut()"
      (blur)="changeBlur($event)"
    ></textarea>

    <!-- Error messages -->
    <mat-error *ngIf="submitted && errors" [id]="errorFieldId">
      <ng-container *ngFor="let item of errors || {} | keyvalue; let i = index">
        <div *ngIf="i === 0" class="error-message">
          {{ errorMessages[item.key] || item.value?.message }}
        </div>
      </ng-container>
      <ng-container *ngIf="errors as errors">
        <div *ngIf="errors['custom']?.status" class="error-message">
          {{ errors["custom"].message }}
        </div>
      </ng-container>
    </mat-error>
  </mat-form-field>

  <!-- Hover error display for better UX -->
  <div
    *ngIf="submitted && errors && isHovered && !readonly"
    class="hover-error-tooltip"
    [id]="errorFieldId + '_hover'"
  >
    <ng-container *ngFor="let item of errors || {} | keyvalue; let i = index">
      <div *ngIf="i === 0" class="tooltip-error-text">
        {{ errorMessages[item.key] || item.value?.message }}
      </div>
    </ng-container>
    <ng-container *ngIf="errors as errors">
      <div *ngIf="errors['custom']?.status" class="tooltip-error-text">
        {{ errors["custom"].message }}
      </div>
    </ng-container>
  </div>
</div>
