@use '@angular/material' as mat;
@use './theme-colors' as theme-colors;

// Include the common styles for Angular Material
@include mat.core();

// Define the custom theme using our color palette
$custom-theme: mat.define-theme((
  color: (
    theme-type: light,
    primary: theme-colors.$primary-palette,
    tertiary: theme-colors.$tertiary-palette,
  ),
  typography: (
    brand-family: 'Open Sans',
    plain-family: 'Open Sans',
  ),
  density: (
    scale: 0,
  )
));

// Apply the theme to all Material components
@include mat.all-component-themes($custom-theme);

// Custom Material component overrides using our CSS variables
:root {
  // Override Material Design color tokens with our theme
  --mat-form-field-container-color: var(--color-secondary-shade);
  --mat-form-field-disabled-container-color: var(--color-secondary-shade);
  --mat-form-field-label-text-color: var(--color-text-medium);
  --mat-form-field-disabled-label-text-color: var(--color-text-light);
  --mat-form-field-input-text-color: var(--color-text-dark);
  --mat-form-field-disabled-input-text-color: var(--color-text-light);
  --mat-form-field-placeholder-text-color: var(--color-text-light);
  --mat-form-field-error-text-color: var(--color-poppy-red);
  --mat-form-field-focus-color: var(--color-tertiary);
  --mat-form-field-hover-color: var(--color-secondary);
  
  // Button overrides
  --mat-filled-button-container-color: var(--color-tertiary);
  --mat-filled-button-label-text-color: white;
  --mat-filled-button-hover-container-color: var(--color-secondary);
  --mat-filled-button-focus-container-color: var(--color-secondary);
  --mat-filled-button-pressed-container-color: var(--color-secondary);
  
  --mat-outlined-button-outline-color: var(--color-secondary);
  --mat-outlined-button-label-text-color: var(--color-text-dark);
  --mat-outlined-button-hover-container-color: var(--color-secondary-shade);
  --mat-outlined-button-focus-container-color: var(--color-secondary-shade);
  
  // Radio button overrides
  --mat-radio-checked-ripple-color: var(--color-tertiary);
  --mat-radio-selected-icon-color: var(--color-tertiary);
  --mat-radio-unselected-icon-color: var(--color-secondary);
  --mat-radio-disabled-selected-icon-color: var(--color-text-light);
  --mat-radio-disabled-unselected-icon-color: var(--color-text-light);
  
  // Select overrides
  --mat-select-panel-background-color: var(--color-primary);
  --mat-select-enabled-trigger-text-color: var(--color-text-dark);
  --mat-select-disabled-trigger-text-color: var(--color-text-light);
  --mat-select-placeholder-text-color: var(--color-text-light);
  --mat-select-enabled-arrow-color: var(--color-secondary);
  --mat-select-disabled-arrow-color: var(--color-text-light);
  
  // Option overrides
  --mat-option-label-text-color: var(--color-text-dark);
  --mat-option-hover-state-layer-color: var(--color-secondary-shade);
  --mat-option-focus-state-layer-color: var(--color-secondary-shade);
  --mat-option-selected-state-layer-color: var(--color-tertiary-shade);
  
  // Datepicker overrides
  --mat-datepicker-calendar-container-background-color: var(--color-primary);
  --mat-datepicker-calendar-date-text-color: var(--color-text-dark);
  --mat-datepicker-calendar-date-selected-state-background-color: var(--color-tertiary);
  --mat-datepicker-calendar-date-selected-state-text-color: white;
  --mat-datepicker-calendar-date-today-outline-color: var(--color-secondary);
  --mat-datepicker-calendar-date-hover-state-background-color: var(--color-secondary-shade);
  --mat-datepicker-calendar-date-focus-state-background-color: var(--color-secondary-shade);
  
  // Table overrides
  --mat-table-background-color: var(--color-primary);
  --mat-table-header-container-background-color: var(--color-secondary-shade);
  --mat-table-row-item-label-text-color: var(--color-text-dark);
  --mat-table-header-headline-color: var(--color-text-dark);
}

// Additional Material component styling
.mat-mdc-form-field {
  .mat-mdc-text-field-wrapper {
    background-color: var(--color-secondary-shade) !important;
    border-radius: 0.375rem;
    
    &:hover {
      background-color: var(--color-secondary-shade) !important;
    }
  }
  
  .mat-mdc-form-field-focus-overlay {
    background-color: transparent !important;
  }
  
  .mat-mdc-form-field-bottom-align::before {
    display: none;
  }
}

.mat-mdc-outlined-button {
  border-color: var(--color-secondary) !important;
  color: var(--color-text-dark) !important;
  
  &:hover {
    background-color: var(--color-secondary-shade) !important;
    border-color: var(--color-secondary) !important;
  }
}

.mat-mdc-raised-button {
  background-color: var(--color-tertiary) !important;
  color: white !important;
  
  &:hover {
    background-color: var(--color-secondary) !important;
  }
}

.mat-mdc-radio-button {
  .mdc-radio__native-control:enabled:checked + .mdc-radio__background .mdc-radio__outer-circle {
    border-color: var(--color-tertiary) !important;
  }
  
  .mdc-radio__native-control:enabled + .mdc-radio__background .mdc-radio__inner-circle {
    border-color: var(--color-tertiary) !important;
  }
  
  .mdc-radio__native-control:enabled:checked + .mdc-radio__background .mdc-radio__inner-circle {
    background-color: var(--color-tertiary) !important;
  }
}

.mat-mdc-select-panel {
  background-color: var(--color-primary) !important;
  border-radius: 0.375rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.mat-mdc-option {
  color: var(--color-text-dark) !important;
  
  &:hover {
    background-color: var(--color-secondary-shade) !important;
  }
  
  &.mdc-list-item--selected {
    background-color: var(--color-tertiary-shade) !important;
    color: var(--color-text-dark) !important;
  }
}

.mat-datepicker-popup {
  .mat-calendar {
    background-color: var(--color-primary) !important;
  }
  
  .mat-calendar-body-selected {
    background-color: var(--color-tertiary) !important;
    color: white !important;
  }
  
  .mat-calendar-body-today:not(.mat-calendar-body-selected) {
    border-color: var(--color-secondary) !important;
  }
}
