.form-group {
  margin-bottom: 1rem;
  position: relative;
  width: 100%;

  .readonly-container {
    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: var(--color-text-dark);
      font-size: 0.875rem;
      line-height: 1.25rem;

      &.bold-label {
        font-weight: 600;
        color: var(--color-text-dark);
      }

      .required-indicator {
        color: var(--color-poppy-red);
        margin-left: 0.125rem;
      }
    }

    .show-data {
      padding: 0.75rem;
      background-color: var(--color-secondary-shade);
      border: 1px solid var(--color-secondary);
      border-radius: 0.375rem;
      color: var(--color-text-dark);
      font-size: 0.875rem;
      line-height: 1.5;
      min-height: 3rem;
      display: flex;
      align-items: center;
    }
  }

  // Hover error tooltip
  .hover-error-tooltip {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--color-poppy-red);
    color: white;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    margin-top: 0.25rem;

    .tooltip-error-text {
      font-size: 0.75rem;
      line-height: 1.4;
    }

    &::before {
      content: '';
      position: absolute;
      top: -4px;
      left: 1rem;
      width: 0;
      height: 0;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      border-bottom: 4px solid var(--color-poppy-red);
    }
  }
}

// Global Material overrides for this component
:host ::ng-deep {
  .mat-mdc-form-field {
    width: 100%;

    .mat-mdc-form-field-label {
      color: var(--color-text-medium) !important;

      .required-indicator {
        color: var(--color-poppy-red);
        margin-left: 0.125rem;
      }
    }

    .mat-mdc-text-field-wrapper {
      background-color: var(--color-secondary-shade) !important;
      border-radius: 0.375rem;

      .mat-mdc-form-field-input-control {
        color: var(--color-text-dark) !important;

        &::placeholder {
          color: var(--color-text-light) !important;
        }
      }
    }

    .mat-mdc-form-field-outline {
      color: var(--color-secondary) !important;
    }

    &.mat-focused {
      .mat-mdc-form-field-outline-thick {
        color: var(--color-tertiary) !important;
      }

      .mat-mdc-form-field-label {
        color: var(--color-tertiary) !important;
      }
    }

    &.mat-form-field-invalid {
      .mat-mdc-form-field-outline-thick {
        color: var(--color-poppy-red) !important;
      }

      .mat-mdc-form-field-label {
        color: var(--color-poppy-red) !important;
      }
    }

    .mat-mdc-form-field-error {
      color: var(--color-poppy-red) !important;
      font-size: 0.75rem;
      margin-top: 0.25rem;

      .error-message {
        font-size: 0.75rem;
        line-height: 1.4;
      }
    }
  }

  .mat-mdc-form-field-appearance-outline {
    .mat-mdc-form-field-outline {
      color: var(--color-secondary);
    }

    &.mat-focused .mat-mdc-form-field-outline-thick {
      color: var(--color-tertiary);
    }

    &.mat-form-field-invalid .mat-mdc-form-field-outline-thick {
      color: var(--color-poppy-red);
    }
  }

  .mat-mdc-text-field-wrapper {
    background-color: var(--color-secondary-shade) !important;

    &:hover {
      background-color: var(--color-secondary-shade) !important;
    }
  }

  .mat-mdc-form-field-input-control {
    color: var(--color-text-dark) !important;

    &::placeholder {
      color: var(--color-text-light) !important;
    }
  }
}

