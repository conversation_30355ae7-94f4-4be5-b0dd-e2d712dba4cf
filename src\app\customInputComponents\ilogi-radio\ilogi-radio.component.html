<div class="form-group">
  <!-- Read-only display -->
  <div *ngIf="readonly" class="readonly-container">
    <label *ngIf="!hideLabel" [class.bold-label]="readonly">
      {{fieldLabel}} <span *ngIf="mandatory" class="required-indicator">*</span>
    </label>
    <div class="show-data" *ngIf="!fieldExactVal">{{ value === '1' ? 'Yes' : (value === '0' ? 'No' : value) }}</div>
    <div class="show-data" *ngIf="fieldExactVal">{{ fieldExactVal }}</div>
  </div>

  <!-- Material UI Radio Group -->
  <div *ngIf="!readonly" class="radio-field-container">
    <label *ngIf="!hideLabel" class="field-label">
      {{fieldLabel}} <span *ngIf="mandatory" class="required-indicator">*</span>
    </label>

    <mat-radio-group
      [id]="fieldId"
      [value]="value"
      (change)="onChangeControl($event.value)"
      (mouseenter)="showErrorOnFieldHover()"
      (mouseleave)="hideErrorOnFieldHoverOut()"
      [ngClass]="{ 'is-invalid': submitted && errors }"
      [disabled]="isDisabled"
      color="primary"
      aria-label="Select an option"
      [attr.aria-invalid]="submitted && errors ? 'true' : 'false'"
      [attr.aria-describedby]="errorFieldId"
      class="radio-group">
      <mat-radio-button
        *ngFor="let radio of radioOptions"
        [value]="radio.value"
        class="radio-option">
        {{ radio.name }}
      </mat-radio-button>
    </mat-radio-group>

    <!-- Error messages -->
    <div *ngIf="submitted && errors" class="error-container" [id]="errorFieldId">
      <ng-container *ngFor="let item of (errors || {} | keyvalue); let i = index">
        <div *ngIf="i === 0" class="error-message">
          {{ errorMessages[item.key] || item.value?.message }}
        </div>
      </ng-container>
      <ng-container *ngIf="errors as errors">
        <div *ngIf="errors['custom']?.status" class="error-message">
          {{ errors['custom'].message }}
        </div>
      </ng-container>
    </div>
  </div>

  <!-- Hover error display for better UX -->
  <div
    *ngIf="submitted && errors && isHovered && !readonly"
    class="hover-error-tooltip"
    [id]="errorFieldId + '_hover'"
  >
    <ng-container *ngFor="let item of (errors || {} | keyvalue); let i = index">
      <div *ngIf="i === 0" class="tooltip-error-text">
        {{ errorMessages[item.key] || item.value?.message }}
      </div>
    </ng-container>
    <ng-container *ngIf="errors as errors">
      <div *ngIf="errors['custom']?.status" class="tooltip-error-text">
        {{ errors['custom'].message }}
      </div>
    </ng-container>
  </div>
</div>

