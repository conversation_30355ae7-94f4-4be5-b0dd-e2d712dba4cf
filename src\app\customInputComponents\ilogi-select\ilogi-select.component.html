<div class="form-group">
  <!-- Read-only display -->
  <div *ngIf="readonly" class="readonly-container">
    <label *ngIf="!hideLabel" [class.bold-label]="readonly">
      {{fieldLabel}} <span *ngIf="mandatory" class="required-indicator">*</span>
    </label>
    <div class="show-data" *ngIf="!fieldExactVal">{{ getDisplayName(value) }}</div>
    <div class="show-data" *ngIf="fieldExactVal">{{ fieldExactVal }}</div>
  </div>

  <!-- Material UI Form Field for select -->
  <mat-form-field *ngIf="!readonly" appearance="outline" class="w-100">
    <mat-label *ngIf="!hideLabel">
      {{fieldLabel}} <span *ngIf="mandatory" class="required-indicator">*</span>
    </mat-label>

    <mat-select
      [id]="fieldId"
      [value]="value"
      (selectionChange)="onChangeControl($event.value)"
      (mouseenter)="showErrorOnFieldHover()"
      (mouseleave)="hideErrorOnFieldHoverOut()"
      [ngClass]="{ 'is-invalid': submitted && errors }"
      [disabled]="isDisabled"
      [placeholder]="placeholder"
      aria-label="Select an option"
      [attr.aria-invalid]="submitted && errors ? 'true' : 'false'"
      [attr.aria-describedby]="errorFieldId">
      <mat-option *ngFor="let option of selectOptions" [value]="option.id" [title]="option.name">
        {{ option.name.length > 70 ? option.name.substring(0, 70) + '...' : option.name }}
      </mat-option>
    </mat-select>

    <!-- Error messages -->
    <mat-error *ngIf="submitted && errors" [id]="errorFieldId">
      <ng-container *ngFor="let item of (errors || {} | keyvalue); let i = index">
        <div *ngIf="i === 0" class="error-message">
          {{ errorMessages[item.key] || item.value?.message }}
        </div>
      </ng-container>
      <ng-container *ngIf="errors as errors">
        <div *ngIf="errors['custom']?.status" class="error-message">
          {{ errors['custom'].message }}
        </div>
      </ng-container>
    </mat-error>
  </mat-form-field>

  <!-- Hover error display for better UX -->
  <div
    *ngIf="submitted && errors && isHovered && !readonly"
    class="hover-error-tooltip"
    [id]="errorFieldId + '_hover'"
  >
    <ng-container *ngFor="let item of (errors || {} | keyvalue); let i = index">
      <div *ngIf="i === 0" class="tooltip-error-text">
        {{ errorMessages[item.key] || item.value?.message }}
      </div>
    </ng-container>
    <ng-container *ngIf="errors as errors">
      <div *ngIf="errors['custom']?.status" class="tooltip-error-text">
        {{ errors['custom'].message }}
      </div>
    </ng-container>
  </div>
</div>
