.form-group {
  margin-bottom: 1rem;
  position: relative;
  width: 100%;

  .readonly-container {
    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: var(--color-text-dark);
      font-size: 0.875rem;
      line-height: 1.25rem;

      &.bold-label {
        font-weight: 600;
        color: var(--color-text-dark);
      }

      .required-indicator {
        color: var(--color-poppy-red);
        margin-left: 0.125rem;
      }
    }

    .show-data {
      padding: 0.75rem;
      background-color: var(--color-secondary-shade);
      border: 1px solid var(--color-secondary);
      border-radius: 0.375rem;
      color: var(--color-text-dark);
      font-size: 0.875rem;
      line-height: 1.5;
      min-height: 3rem;
      display: flex;
      align-items: center;
    }
  }

  .radio-field-container {
    .field-label {
      display: block;
      margin-bottom: 0.75rem;
      font-weight: 500;
      color: var(--color-text-dark);
      font-size: 0.875rem;
      line-height: 1.25rem;

      .required-indicator {
        color: var(--color-poppy-red);
        margin-left: 0.125rem;
      }
    }

    .radio-group {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      padding: 0.5rem;
      background-color: var(--color-secondary-shade);
      border: 1px solid var(--color-secondary);
      border-radius: 0.375rem;
      transition: border-color 0.2s ease;

      &.is-invalid {
        border-color: var(--color-poppy-red);
        background-color: rgba(255, 107, 107, 0.05);
      }

      .radio-option {
        margin-bottom: 0.25rem;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .error-container {
      margin-top: 0.5rem;

      .error-message {
        color: var(--color-poppy-red);
        font-size: 0.75rem;
        line-height: 1.4;
      }
    }
  }

  // Hover error tooltip
  .hover-error-tooltip {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--color-poppy-red);
    color: white;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    margin-top: 0.25rem;

    .tooltip-error-text {
      font-size: 0.75rem;
      line-height: 1.4;
    }

    &::before {
      content: '';
      position: absolute;
      top: -4px;
      left: 1rem;
      width: 0;
      height: 0;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      border-bottom: 4px solid var(--color-poppy-red);
    }
  }
}

// Global Material overrides for this component
:host ::ng-deep {
  .mat-mdc-radio-button {
    .mdc-radio {
      .mdc-radio__native-control:enabled:checked + .mdc-radio__background .mdc-radio__outer-circle {
        border-color: var(--color-tertiary) !important;
      }

      .mdc-radio__native-control:enabled + .mdc-radio__background .mdc-radio__inner-circle {
        border-color: var(--color-tertiary) !important;
      }

      .mdc-radio__native-control:enabled:checked + .mdc-radio__background .mdc-radio__inner-circle {
        background-color: var(--color-tertiary) !important;
      }

      .mdc-radio__native-control:enabled:not(:checked) + .mdc-radio__background .mdc-radio__outer-circle {
        border-color: var(--color-secondary) !important;
      }
    }

    .mdc-form-field > label {
      color: var(--color-text-dark) !important;
    }

    &:hover .mdc-radio .mdc-radio__native-control:enabled:not(:checked) + .mdc-radio__background .mdc-radio__outer-circle {
      border-color: var(--color-tertiary) !important;
    }

    &.mat-mdc-radio-disabled {
      .mdc-radio .mdc-radio__native-control:disabled + .mdc-radio__background .mdc-radio__outer-circle {
        border-color: var(--color-text-light) !important;
      }

      .mdc-form-field > label {
        color: var(--color-text-light) !important;
      }
    }
  }

  .mat-mdc-radio-group.is-invalid {
    .mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled + .mdc-radio__background .mdc-radio__outer-circle {
      border-color: var(--color-poppy-red) !important;
    }
  }
}
