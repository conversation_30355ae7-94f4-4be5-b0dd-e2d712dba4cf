@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap');
@import url('https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/dist/feather.min.css');

/* =====================
   Root Variables
====================== */
:root {
  /* Main Theme Colors - 5 Colors Only */
  --color-primary: #fefae0;        /* 60% main color */
  --color-secondary: #ccd5ae;      /* 30% secondary color */
  --color-secondary-shade: #e9edc9; /* highlight color for the 30% */
  --color-tertiary: #d4a373;       /* 10% focus color */
  --color-tertiary-shade: #faedcd; /* shade and focus color of the 10% */

  /* Text Colors - 3 Black Shades */
  --color-text-dark: #1a1a1a;      /* Darkest text */
  --color-text-medium: #4a4a4a;    /* Medium text */
  --color-text-light: #7a7a7a;     /* Light text */

  /* Supporting Poppy Colors - 5 Colors */
  --color-poppy-red: #ff6b6b;      /* Bright red */
  --color-poppy-orange: #ff8e53;   /* Bright orange */
  --color-poppy-yellow: #ffd93d;   /* Bright yellow */
  --color-poppy-green: #6bcf7f;    /* Bright green */
  --color-poppy-blue: #4ecdc4;     /* Bright blue */
}

/* =====================
   Global Styles
====================== */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Open Sans', sans-serif;
  background-color: var(--color-primary);
  color: var(--color-text-dark);
  margin: 0;
  line-height: 1.6;
}

.main-container {
  padding: 2rem;
  background-color: var(--color-primary);
  color: var(--color-text-dark);
  min-height: 100vh;
}

.page-layout {
  background-color: var(--color-tertiary-shade);
  min-height: 100vh;
}

/* =====================
   Form Elements Global Styling
====================== */
button:not(.mat-mdc-button):not(.mat-mdc-raised-button):not(.mat-mdc-outlined-button):not(.btn) {
  background-color: var(--color-tertiary);
  color: white;
  border: 1px solid var(--color-tertiary);
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background-color: var(--color-secondary);
    border-color: var(--color-secondary);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

input:not(.mat-mdc-input-element),
textarea:not(.mat-mdc-input-element),
select:not(.mat-mdc-select) {
  background-color: var(--color-secondary-shade);
  border: 1px solid var(--color-secondary);
  color: var(--color-text-dark);
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;

  &:focus {
    border-color: var(--color-tertiary);
    outline: none;
    box-shadow: 0 0 0 3px rgba(212, 163, 115, 0.1);
  }

  &::placeholder {
    color: var(--color-text-light);
  }
}

/* =====================
   Utility Classes
====================== */
.text-dark { color: var(--color-text-dark); }
.text-medium { color: var(--color-text-medium); }
.text-light { color: var(--color-text-light); }

.bg-primary { background-color: var(--color-primary); }
.bg-secondary { background-color: var(--color-secondary); }
.bg-secondary-shade { background-color: var(--color-secondary-shade); }
.bg-tertiary { background-color: var(--color-tertiary); }
.bg-tertiary-shade { background-color: var(--color-tertiary-shade); }

/* Poppy Color Utilities */
.text-poppy-red { color: var(--color-poppy-red); }
.text-poppy-orange { color: var(--color-poppy-orange); }
.text-poppy-yellow { color: var(--color-poppy-yellow); }
.text-poppy-green { color: var(--color-poppy-green); }
.text-poppy-blue { color: var(--color-poppy-blue); }

.bg-poppy-red { background-color: var(--color-poppy-red); }
.bg-poppy-orange { background-color: var(--color-poppy-orange); }
.bg-poppy-yellow { background-color: var(--color-poppy-yellow); }
.bg-poppy-green { background-color: var(--color-poppy-green); }
.bg-poppy-blue { background-color: var(--color-poppy-blue); }

/* Border Color Utilities */
.border-primary { border-color: var(--color-primary); }
.border-secondary { border-color: var(--color-secondary); }
.border-secondary-shade { border-color: var(--color-secondary-shade); }
.border-tertiary { border-color: var(--color-tertiary); }
.border-tertiary-shade { border-color: var(--color-tertiary-shade); }

.border-poppy-red { border-color: var(--color-poppy-red); }
.border-poppy-orange { border-color: var(--color-poppy-orange); }
.border-poppy-yellow { border-color: var(--color-poppy-yellow); }
.border-poppy-green { border-color: var(--color-poppy-green); }
.border-poppy-blue { border-color: var(--color-poppy-blue); }

/* Hover Utilities */
.hover\:bg-secondary:hover { background-color: var(--color-secondary); }
.hover\:bg-tertiary:hover { background-color: var(--color-tertiary); }
.hover\:text-tertiary:hover { color: var(--color-tertiary); }

/* =====================
   Global Material UI Overrides
====================== */

/* Material Form Field Global Overrides */
.mat-mdc-form-field {
  .mat-mdc-text-field-wrapper {
    background-color: var(--color-secondary-shade) !important;
    border-radius: 0.375rem;
  }

  .mat-mdc-form-field-outline {
    color: var(--color-secondary) !important;
  }

  &.mat-focused {
    .mat-mdc-form-field-outline-thick {
      color: var(--color-tertiary) !important;
    }

    .mat-mdc-form-field-label {
      color: var(--color-tertiary) !important;
    }
  }

  &.mat-form-field-invalid {
    .mat-mdc-form-field-outline-thick {
      color: var(--color-poppy-red) !important;
    }

    .mat-mdc-form-field-label {
      color: var(--color-poppy-red) !important;
    }
  }

  .mat-mdc-form-field-label {
    color: var(--color-text-medium) !important;
  }

  .mat-mdc-form-field-input-control {
    color: var(--color-text-dark) !important;

    &::placeholder {
      color: var(--color-text-light) !important;
    }
  }

  .mat-mdc-form-field-error {
    color: var(--color-poppy-red) !important;
    font-size: 0.75rem;
  }
}

/* Material Button Global Overrides */
.mat-mdc-raised-button {
  background-color: var(--color-tertiary) !important;
  color: white !important;

  &:hover {
    background-color: var(--color-secondary) !important;
  }

  &.mat-primary {
    background-color: var(--color-tertiary) !important;
  }

  &.mat-accent {
    background-color: var(--color-tertiary) !important;
  }
}

.mat-mdc-outlined-button {
  border-color: var(--color-secondary) !important;
  color: var(--color-text-dark) !important;

  &:hover {
    background-color: var(--color-secondary-shade) !important;
    border-color: var(--color-secondary) !important;
  }
}

.mat-mdc-button {
  color: var(--color-text-dark) !important;

  &:hover {
    background-color: var(--color-secondary-shade) !important;
  }
}

/* Material Radio Button Global Overrides */
.mat-mdc-radio-button {
  .mdc-radio {
    .mdc-radio__native-control:enabled:checked + .mdc-radio__background .mdc-radio__outer-circle {
      border-color: var(--color-tertiary) !important;
    }

    .mdc-radio__native-control:enabled:checked + .mdc-radio__background .mdc-radio__inner-circle {
      background-color: var(--color-tertiary) !important;
    }

    .mdc-radio__native-control:enabled:not(:checked) + .mdc-radio__background .mdc-radio__outer-circle {
      border-color: var(--color-secondary) !important;
    }
  }

  .mdc-form-field > label {
    color: var(--color-text-dark) !important;
  }
}

/* Material Select Global Overrides */
.mat-mdc-select {
  color: var(--color-text-dark) !important;

  .mat-mdc-select-arrow {
    color: var(--color-secondary) !important;
  }
}

.mat-mdc-select-panel {
  background-color: var(--color-primary) !important;
  border-radius: 0.375rem;
}

.mat-mdc-option {
  color: var(--color-text-dark) !important;

  &:hover {
    background-color: var(--color-secondary-shade) !important;
  }

  &.mdc-list-item--selected {
    background-color: var(--color-tertiary-shade) !important;
    color: var(--color-text-dark) !important;
  }
}

/* Material Datepicker Global Overrides */
.mat-datepicker-popup {
  .mat-calendar {
    background-color: var(--color-primary) !important;
  }

  .mat-calendar-body-selected {
    background-color: var(--color-tertiary) !important;
    color: white !important;
  }

  .mat-calendar-body-today:not(.mat-calendar-body-selected) {
    border-color: var(--color-secondary) !important;
  }

  .mat-calendar-body-cell:not(.mat-calendar-body-disabled):hover > .mat-calendar-body-cell-content:not(.mat-calendar-body-selected) {
    background-color: var(--color-secondary-shade) !important;
  }
}

.mat-datepicker-toggle {
  color: var(--color-secondary) !important;

  &:hover {
    color: var(--color-tertiary) !important;
  }
}

/* Material Table Global Overrides */
.mat-mdc-table {
  background-color: var(--color-primary) !important;

  .mat-mdc-header-row {
    background-color: var(--color-secondary-shade) !important;
  }

  .mat-mdc-row:hover {
    background-color: var(--color-secondary-shade) !important;
  }

  .mat-mdc-cell, .mat-mdc-header-cell {
    color: var(--color-text-dark) !important;
  }
}

/* Material Snackbar Global Overrides */
.mat-mdc-snack-bar-container {
  background-color: var(--color-text-dark) !important;
  color: var(--color-primary) !important;

  &.mat-accent {
    background-color: var(--color-tertiary) !important;
    color: white !important;
  }

  &.mat-warn {
    background-color: var(--color-poppy-red) !important;
    color: white !important;
  }
}





// @use '@angular/material' as mat;
// @use "../theme-colors" as theme-colors;
//   @import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap');
// @import url('https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/dist/feather.min.css');

// :root {
//   @include mat.datepicker-overrides((
//     calendar-body-label-text-color: var(--color-warning),
//     calendar-container-background-color: var(--color-brand-dark),
//   ));


//   /* Base Colors */
//   --color-primary-bg: hsl(0, 0%, 100%); // #ffffff - Main white background
//   --color-card-bg: hsl(0, 0%, 100%); // #ffffff - Card background
//   --color-surface: hsl(0, 0%, 98%); // #fafafa - Light surface for subtle sections

//   /* Brand & Accent Colors (Deep Red Theme) */
//   --color-brand-primary: hsl(348, 99%, 33%); // Deep red for primary actions
//   --color-brand-secondary: hsl(348, 85%, 45%); // Medium red for secondary elements
//   --color-brand-accent: hsl(348, 100%, 40%); // Vibrant red for highlights
//   --color-brand-light: hsl(348, 100%, 95%); // Very light pink for hover states
//   --color-brand-dark: hsl(348, 99%, 25%); // Darker red for pressed states

//   /* Complementary Colors (Teal accents) */
//   --color-complementary-primary: hsl(168, 99%, 33%); // Complementary teal-green
//   --color-complementary-secondary: hsl(168, 85%, 45%); // Medium teal
//   --color-complementary-light: hsl(168, 100%, 95%); // Light teal background

//   /* Semantic Colors */
//   --color-success: hsl(142, 76%, 45%); // Green for success states
//   --color-warning: hsl(45, 100%, 55%); // Amber for warnings
//   --color-error: hsl(348, 99%, 33%); // Using brand red for errors
//   --color-info: hsl(200, 100%, 50%); // Cyan for information

//   /* Text Colors */
//   --color-text-primary: hsl(0, 0%, 13%); // Near black for main text
//   --color-text-secondary: hsl(0, 0%, 40%); // Medium gray for secondary text
//   --color-text-muted: hsl(0, 0%, 60%); // Light gray for muted text
//   --color-text-inverse: hsl(0, 0%, 100%); // White text for dark backgrounds
//   --color-text-link: hsl(348, 99%, 33%); // Red for links
//   --color-text-link-hover: hsl(348, 99%, 25%); // Darker red for link hover

//   /* Button Colors */
//   /* Primary Button (Red) */
//   --color-btn-primary-bg: hsl(348, 99%, 33%);
//   --color-btn-primary-hover: hsl(348, 99%, 25%);
//   --color-btn-primary-active: hsl(348, 99%, 20%);
//   --color-btn-primary-text: hsl(0, 0%, 100%);

//   /* Secondary Button (White with Red border) */
//   --color-btn-secondary-bg: hsl(0, 0%, 100%);
//   --color-btn-secondary-hover: hsl(348, 100%, 98%);
//   --color-btn-secondary-active: hsl(348, 100%, 95%);
//   --color-btn-secondary-text: hsl(348, 99%, 33%);
//   --color-btn-secondary-border: hsl(348, 99%, 33%);

//   /* Outline Button */
//   --color-btn-outline-bg: transparent;
//   --color-btn-outline-hover: hsl(348, 100%, 98%);
//   --color-btn-outline-active: hsl(348, 100%, 95%);
//   --color-btn-outline-text: hsl(348, 99%, 33%);
//   --color-btn-outline-border: hsl(348, 99%, 33%);

//   /* Ghost Button */
//   --color-btn-ghost-bg: transparent;
//   --color-btn-ghost-hover: hsl(348, 100%, 98%);
//   --color-btn-ghost-active: hsl(348, 100%, 95%);
//   --color-btn-ghost-text: hsl(348, 99%, 33%);

//   /* Danger Button (Same as primary for consistency) */
//   --color-btn-danger-bg: hsl(348, 99%, 33%);
//   --color-btn-danger-hover: hsl(348, 99%, 25%);
//   --color-btn-danger-active: hsl(348, 99%, 20%);
//   --color-btn-danger-text: hsl(0, 0%, 100%);

//   /* Border Colors */
//   --color-border-light: hsl(0, 0%, 90%); // Light gray borders
//   --color-border-medium: hsl(0, 0%, 80%); // Medium gray borders
//   --color-border-dark: hsl(0, 0%, 70%); // Dark gray borders
//   --color-border-focus: hsl(348, 99%, 33%); // Red focus ring color

//   /* Form Colors */
//   --color-form-input-bg: hsl(0, 0%, 100%);
//   --color-form-input-border: hsl(0, 0%, 85%);
//   --color-form-input-border-hover: hsl(0, 0%, 75%);
//   --color-form-input-border-focus: hsl(348, 99%, 33%);
//   --color-form-input-text: hsl(0, 0%, 13%);
//   --color-form-input-placeholder: hsl(0, 0%, 60%);

//   /* Form Validation States */
//   --color-form-input-success: hsl(142, 76%, 45%);
//   --color-form-input-warning: hsl(45, 100%, 55%);
//   --color-form-input-error: hsl(348, 99%, 33%);

//   /* Navigation Colors */
//   --color-nav-bg: hsl(0, 0%, 100%);
//   --color-nav-border: hsl(0, 0%, 90%);
//   --color-nav-link-text: hsl(0, 0%, 40%);
//   --color-nav-link-hover: hsl(348, 99%, 33%);
//   --color-nav-link-active: hsl(348, 99%, 33%);
//   --color-nav-link-active-bg: hsl(348, 100%, 97%);

//   /* Table Colors */
//   --color-table-header-bg: hsl(0, 0%, 98%);
//   --color-table-header-text: hsl(0, 0%, 13%);
//   --color-table-row-even: hsl(0, 0%, 100%);
//   --color-table-row-odd: hsl(0, 0%, 99%);
//   --color-table-row-hover: hsl(348, 100%, 98%);
//   --color-table-border: hsl(0, 0%, 90%);

//   /* Status Colors */
//   --color-status-online: hsl(142, 76%, 45%);
//   --color-status-pending: hsl(45, 100%, 55%);
//   --color-status-offline: hsl(0, 0%, 60%);
//   --color-status-draft: hsl(348, 99%, 33%);

//   /* Shadow Colors */
//   --shadow-light: hsla(0, 0%, 0%, 0.05);
//   --shadow-medium: hsla(0, 0%, 0%, 0.1);
//   --shadow-dark: hsla(0, 0%, 0%, 0.15);
//   --shadow-focus: hsla(348, 99%, 33%, 0.25);

//   /* Overlay Colors */
//   --color-overlay-light: hsla(0, 0%, 0%, 0.3);
//   --color-overlay-medium: hsla(0, 0%, 0%, 0.5);
//   --color-overlay-dark: hsla(0, 0%, 0%, 0.7);

//   /* Notification Colors */
//   --color-notification-success-bg: hsl(142, 76%, 95%);
//   --color-notification-success-border: hsl(142, 76%, 45%);
//   --color-notification-success-text: hsl(142, 76%, 30%);

//   --color-notification-warning-bg: hsl(45, 100%, 95%);
//   --color-notification-warning-border: hsl(45, 100%, 55%);
//   --color-notification-warning-text: hsl(45, 100%, 35%);

//   --color-notification-error-bg: hsl(348, 100%, 95%);
//   --color-notification-error-border: hsl(348, 99%, 33%);
//   --color-notification-error-text: hsl(348, 99%, 25%);

//   --color-notification-info-bg: hsl(200, 100%, 95%);
//   --color-notification-info-border: hsl(200, 100%, 50%);
//   --color-notification-info-text: hsl(200, 100%, 35%);

//   /* Utility Colors */
//   --color-utility-disabled: hsl(0, 0%, 85%);
//   --color-utility-disabled-text: hsl(0, 0%, 60%);
//   --color-utility-backdrop: hsla(0, 0%, 0%, 0.4);
//   --color-utility-selection: hsla(348, 99%, 33%, 0.2);

//   /* Additional Red Theme Variants */
//   --color-red-50: hsl(348, 100%, 97%);
//   --color-red-100: hsl(348, 100%, 95%);
//   --color-red-200: hsl(348, 96%, 89%);
//   --color-red-300: hsl(348, 94%, 80%);
//   --color-red-400: hsl(348, 91%, 69%);
//   --color-red-500: hsl(348, 85%, 55%);
//   --color-red-600: hsl(348, 99%, 33%); // Your main color
//   --color-red-700: hsl(348, 99%, 25%);
//   --color-red-800: hsl(348, 99%, 20%);
//   --color-red-900: hsl(348, 99%, 15%);

//   /* Gray Scale */
//   --color-gray-50: hsl(0, 0%, 98%);
//   --color-gray-100: hsl(0, 0%, 95%);
//   --color-gray-200: hsl(0, 0%, 90%);
//   --color-gray-300: hsl(0, 0%, 80%);
//   --color-gray-400: hsl(0, 0%, 70%);
//   --color-gray-500: hsl(0, 0%, 60%);
//   --color-gray-600: hsl(0, 0%, 40%);
//   --color-gray-700: hsl(0, 0%, 30%);
//   --color-gray-800: hsl(0, 0%, 20%);
//   --color-gray-900: hsl(0, 0%, 13%);
// }

// // Theme Color Mappings
// $primary-color: var(--color-brand-primary);
// $secondary-color: var(--color-brand-secondary);
// $accent-color: var(--color-brand-accent);
// $light-color: var(--color-brand-light);
// $dark-color: var(--color-brand-dark);

// $success-color: var(--color-success);
// $warning-color: var(--color-warning);
// $danger-color: var(--color-error);
// $info-color: var(--color-info);

// $text-primary: var(--color-text-primary);
// $text-secondary: var(--color-text-secondary);
// $text-muted: var(--color-text-muted);
// $text-inverse: var(--color-text-inverse);
// $text-link: var(--color-text-link);
// $text-link-hover: var(--color-text-link-hover);

// $border-color: var(--color-border-light);
// $border-medium: var(--color-border-medium);
// $border-focus: var(--color-border-focus);

// $btn-primary-bg: var(--color-btn-primary-bg);
// $btn-primary-hover: var(--color-btn-primary-hover);
// $btn-primary-active: var(--color-btn-primary-active);
// $btn-primary-text: var(--color-btn-primary-text);

// $btn-secondary-bg: var(--color-btn-secondary-bg);
// $btn-secondary-text: var(--color-btn-secondary-text);
// $btn-secondary-border: var(--color-btn-secondary-border);

// $box-shadow: 0 4px 8px var(--shadow-light); // Update if needed

// /* ======================
// Main Content Container
// ====================== */
// html {
//   @include mat.theme((
//     color: (
//       primary: theme-colors.$primary-palette,
//       tertiary: theme-colors.$tertiary-palette,
//       theme-type: light,
//     ),
//     typography: Roboto,
//     density: 0
//   ));
// }
// /* ======================
//    Main Content Container
// ====================== */
// .main-container {
//   margin-left: 240px; /* Default for open sidebar on desktop - matches sidebar width */
//   width: calc(100% - 240px);
//   min-height: calc(100vh - 70px);
//   padding: 30px 40px;
//   background-color: var(--color-primary-bg);
//   color: var(--color-text-primary);
//   transition: all 0.3s ease;
//   position: relative;
//   overflow-x: hidden;
// }

// /* Sidebar collapsed on desktop */
// .sidebar-collapsed .main-container {
//   margin-left: 70px; /* Adjust for collapsed sidebar - matches collapsed sidebar width */
//   width: calc(100% - 70px);
// }

// /* Tablet and Mobile: sidebar becomes overlay, so container takes full width */
// @media (max-width: 1024px) {
//   .main-container {
//     margin-left: 0 !important; /* Important to override any other margin settings */
//     width: 100% !important; /* Important to override any other width settings */
//     padding: 25px 30px;
//   }

//   /* When sidebar is visible on mobile, add slight blur/dim effect to main content */
//   body.sidebar-mobile-open .main-container {
//     filter: blur(1px);
//     opacity: 0.8;
//   }
// }

// /* Mobile (large phones) */
// @media (max-width: 768px) {
//   .main-container {
//     padding: 20px 20px;
//   }
// }

// /* Small phones */
// @media (max-width: 480px) {
//   .main-container {
//     padding: 15px 15px;
//   }
// }


// .page-layout {
//   position: relative;
//   min-height: 100vh;
// }

// .main-content {
//   min-height: 100vh;
//   background-color: var(--color-surface, #f8f9fa);

//   .content-wrapper {
//     padding: 1.5rem;
//   }
// }

// /* Prevent scrolling when sidebar is open */
// body.sidebar-open {
//   overflow: hidden;
// }

// @media (max-width: 768px) {
//   .main-content .content-wrapper {
//     padding: 1rem;
//   }
// }
