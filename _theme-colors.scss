// Custom Material Theme using new color palette from styles.scss
@use 'sass:map';
@use '@angular/material' as mat;

// Convert CSS custom properties to SCSS variables for Material theme
// Primary: #fefae0 (60% main color)
// Secondary: #ccd5ae (30% secondary color)
// Tertiary: #d4a373 (10% focus color)

$_palettes: (
  primary: (
    0: #000000,
    10: #2d2a1f,
    20: #44402f,
    25: #50493a,
    30: #5c5345,
    35: #695e51,
    40: #76695c,
    50: #918173,
    60: #ad9a8b,
    70: #c9b5a4,
    80: #e6d1be,
    90: #f5ead9,
    95: #faf4ec,
    98: #fdfbf7,
    99: #fefcfa,
    100: #fefae0, // Primary color
  ),
  secondary: (
    0: #000000,
    10: #1a1f16,
    20: #2f352a,
    25: #3a4034,
    30: #464c3f,
    35: #52584a,
    40: #5e6556,
    50: #777e6d,
    60: #919885,
    70: #acb39e,
    80: #c8cfb8,
    90: #e4ebd3,
    95: #f2f9e1,
    98: #f9fcf0,
    99: #fcfef7,
    100: #ccd5ae, // Secondary color
  ),
  tertiary: (
    0: #000000,
    10: #2b1a0f,
    20: #442f1e,
    25: #513a28,
    30: #5f4532,
    35: #6d513d,
    40: #7c5d48,
    50: #967660,
    60: #b19079,
    70: #cdab93,
    80: #eac7ae,
    90: #f7e3ca,
    95: #fbf1e5,
    98: #fef8f2,
    99: #fffcf9,
    100: #d4a373, // Tertiary color
  ),
  neutral: (
    0: #000000,
    10: #1a1a1a, // Text dark
    20: #303030,
    25: #3c3b3b,
    30: #4a4a4a, // Text medium
    35: #535252,
    40: #5f5e5e,
    50: #7a7a7a, // Text light
    60: #929090,
    70: #adabaa,
    80: #c8c6c5,
    90: #e5e2e1,
    95: #f3f0ef,
    98: #fcf9f8,
    99: #fffbfb,
    100: #ffffff,
    4: #0e0e0e,
    6: #131313,
    12: #202020,
    17: #2a2a2a,
    22: #353535,
    24: #393939,
    87: #dcd9d9,
    92: #eae7e7,
    94: #f0eded,
    96: #f6f3f2,
  ),
  neutral-variant: (
    0: #000000,
    10: #1a1c1c,
    20: #2f3131,
    25: #3a3c3c,
    30: #464747,
    35: #515353,
    40: #5d5e5f,
    50: #767777,
    60: #909191,
    70: #ababab,
    80: #c7c6c6,
    90: #e3e2e2,
    95: #f1f0f0,
    98: #faf9f9,
    99: #fdfcfc,
    100: #ffffff,
  ),
  error: (
    0: #000000,
    10: #410002,
    20: #690005,
    25: #7e0007,
    30: #93000a,
    35: #a80710,
    40: #ba1a1a,
    50: #de3730,
    60: #ff6b6b, // Using poppy red for errors
    70: #ff897d,
    80: #ffb4ab,
    90: #ffdad6,
    95: #ffedea,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
);

$_rest: (
  secondary: map.get($_palettes, secondary),
  neutral: map.get($_palettes, neutral),
  neutral-variant: map.get($_palettes,  neutral-variant),
  error: map.get($_palettes, error),
);

$primary-palette: map.merge(map.get($_palettes, primary), $_rest);
$tertiary-palette: map.merge(map.get($_palettes, tertiary), $_rest);
